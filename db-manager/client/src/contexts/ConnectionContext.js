import React, { createContext, useContext, useState, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { useSnackbar } from "notistack";

const ConnectionContext = createContext();

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "http://localhost:5001/api";

export const ConnectionProvider = ({ children }) => {
  const { enqueueSnackbar } = useSnackbar();
  const queryClient = useQueryClient();
  const [activeConnection, setActiveConnection] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState(null);

  // Fetch connections from the server
  const {
    data: connections = [],
    isLoading: isLoadingConnections,
    error: connectionsError,
  } = useQuery({
    queryKey: ["connections"],
    queryFn: async () => {
      try {
        const { data } = await axios.get(`${API_BASE_URL}/connections`);
        if (Array.isArray(data)) {
          // Filter out any null/undefined connections and ensure they have required properties
          return data.filter(
            (conn) => conn && typeof conn === "object" && conn.id
          );
        }
        return [];
      } catch (error) {
        console.error("Error fetching connections:", error);
        return [];
      }
    },
    retry: 1,
    onError: (error) => {
      enqueueSnackbar("Failed to load connections", { variant: "error" });
      console.error("Error fetching connections:", error);
    },
  });

  // Test a database connection
  const testConnection = useCallback(
    async (connectionConfig) => {
      try {
        setIsConnecting(true);
        setConnectionError(null);

        const response = await axios.post(
          `${API_BASE_URL}/connections/test`,
          connectionConfig
        );

        return response.data;
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Failed to connect to database";
        setConnectionError(errorMessage);
        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      } finally {
        setIsConnecting(false);
      }
    },
    [enqueueSnackbar]
  );

  // Create a new database connection
  const createConnection = useCallback(
    async (connectionData) => {
      try {
        setIsConnecting(true);
        setConnectionError(null);

        const response = await axios.post(
          `${API_BASE_URL}/connections`,
          connectionData
        );

        // Invalidate the connections query to refetch the updated list
        await queryClient.invalidateQueries({ queryKey: ["connections"] });

        enqueueSnackbar("Connection created successfully", {
          variant: "success",
        });
        return response.data;
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Failed to create connection";
        setConnectionError(errorMessage);
        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      } finally {
        setIsConnecting(false);
      }
    },
    [enqueueSnackbar, queryClient]
  );

  // Set the active connection
  const setConnection = useCallback((connection) => {
    setActiveConnection(connection);
    // You might want to store the active connection ID in localStorage
    if (connection) {
      localStorage.setItem("activeConnectionId", connection.id);
    } else {
      localStorage.removeItem("activeConnectionId");
    }
  }, []);

  // Get the active connection from localStorage on initial load
  React.useEffect(() => {
    const savedConnectionId = localStorage.getItem("activeConnectionId");
    if (savedConnectionId && Array.isArray(connections)) {
      const savedConnection = connections.find(
        (c) => c && c.id === savedConnectionId
      );
      if (savedConnection) {
        setActiveConnection(savedConnection);
      }
    }
  }, [connections]);

  // Clear the active connection
  const clearConnection = useCallback(() => {
    setActiveConnection(null);
    localStorage.removeItem("activeConnectionId");
  }, []);

  // Execute a query
  const executeQuery = useCallback(
    async (query, params = []) => {
      if (!activeConnection) {
        throw new Error("No active connection");
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/query/execute`, {
          connectionId: activeConnection.id,
          query,
          params,
        });

        return response.data;
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Query execution failed";
        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      }
    },
    [activeConnection, enqueueSnackbar]
  );

  // Get database schema
  const getSchema = useCallback(async () => {
    if (!activeConnection) {
      throw new Error("No active connection");
    }

    try {
      const response = await axios.get(
        `${API_BASE_URL}/connections/${activeConnection.id}/schema`
      );

      return response.data;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to fetch schema";
      enqueueSnackbar(errorMessage, { variant: "error" });
      throw error;
    }
  }, [activeConnection, enqueueSnackbar]);

  // Update an existing database connection
  const updateConnection = useCallback(
    async (connectionId, connectionData) => {
      try {
        setIsConnecting(true);
        setConnectionError(null);

        const response = await axios.put(
          `${API_BASE_URL}/connections/${connectionId}`,
          connectionData
        );

        // Invalidate the connections query to refetch the updated list
        await queryClient.invalidateQueries({ queryKey: ["connections"] });

        enqueueSnackbar("Connection updated successfully", {
          variant: "success",
        });
        return response.data;
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Failed to update connection";
        setConnectionError(errorMessage);
        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      } finally {
        setIsConnecting(false);
      }
    },
    [enqueueSnackbar, queryClient]
  );

  // Delete a database connection
  const deleteConnection = useCallback(
    async (connectionId) => {
      try {
        setIsConnecting(true);
        setConnectionError(null);

        await axios.delete(`${API_BASE_URL}/connections/${connectionId}`);

        // If the deleted connection was active, clear it
        if (activeConnection?.id === connectionId) {
          setActiveConnection(null);
          localStorage.removeItem("activeConnectionId");
        }

        // Invalidate the connections query to refetch the updated list
        await queryClient.invalidateQueries({ queryKey: ["connections"] });

        enqueueSnackbar("Connection deleted successfully", {
          variant: "success",
        });
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Failed to delete connection";
        setConnectionError(errorMessage);
        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      } finally {
        setIsConnecting(false);
      }
    },
    [activeConnection, enqueueSnackbar, queryClient]
  );

  const value = {
    connections,
    activeConnection,
    isConnecting,
    connectionError,
    isLoadingConnections,
    loading: isLoadingConnections, // alias for compatibility
    error: connectionsError, // alias for compatibility
    testConnection,
    createConnection,
    updateConnection,
    deleteConnection,
    setConnection,
    setActiveConnection: setConnection, // alias for compatibility
    clearConnection,
    executeQuery,
    getSchema,
  };

  return (
    <ConnectionContext.Provider value={value}>
      {children}
    </ConnectionContext.Provider>
  );
};

export const useConnection = () => {
  const context = useContext(ConnectionContext);
  if (!context) {
    throw new Error("useConnection must be used within a ConnectionProvider");
  }

  // Add defensive programming to prevent null reference errors
  return {
    ...context,
    connections: Array.isArray(context.connections)
      ? context.connections.filter(
          (conn) => conn && typeof conn === "object" && conn.id
        )
      : [],
    activeConnection: context.activeConnection || null,
    isConnecting: context.isConnecting || false,
    connectionError: context.connectionError || null,
    isLoadingConnections: context.isLoadingConnections || false,
    loading: context.loading || false,
    error: context.error || null,
  };
};

export default ConnectionContext;
