import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
  CircularProgress,
  IconButton,
  InputAdornment,
  Collapse,
  Alert,
  Paper,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Visibility,
  VisibilityOff,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useConnection } from '../../contexts/ConnectionContext';

const ConnectionDialog = ({ open, onClose }) => {
  const { createConnection, testConnection, isConnecting } = useConnection();
  const [activeTab, setActiveTab] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [connectionString, setConnectionString] = useState('');

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    formik.resetForm();
    setTestResult(null);
  };

  const handleTestConnection = async () => {
    try {
      setTestResult(null);
      const values = formik.values;
      let connectionConfig;

      if (activeTab === 0) {
        // Standard connection form
        connectionConfig = {
          type: values.type,
          name: values.name,
          host: values.host,
          port: values.port,
          database: values.database,
          username: values.username,
          password: values.password,
          ssl: values.ssl,
        };
      } else {
        // Connection string
        connectionConfig = {
          type: values.type,
          name: values.name,
          connectionString: values.connectionString,
        };
      }

      const result = await testConnection(connectionConfig);
      setTestResult({
        success: true,
        message: 'Connection successful!',
        type: result.type,
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: error.message || 'Connection failed. Please check your settings.',
      });
    }
  };

  const handleSubmit = async (values) => {
    try {
      const connectionData = { ...values };
      
      if (activeTab === 1) {
        // For connection string, we just need the type, name, and connection string
        connectionData.connectionString = connectionString;
      }
      
      await createConnection(connectionData);
      onClose();
    } catch (error) {
      console.error('Error creating connection:', error);
    }
  };

  const formik = useFormik({
    initialValues: {
      type: 'postgresql',
      name: '',
      host: 'localhost',
      port: '',
      database: '',
      username: '',
      password: '',
      ssl: false,
      connectionString: '',
    },
    validationSchema: Yup.object({
      type: Yup.string().required('Required'),
      name: Yup.string().required('Required'),
      host: Yup.string().when('type', {
        is: (type) => type !== 'sqlite',
        then: Yup.string().required('Required'),
      }),
      port: Yup.number().when('type', {
        is: (type) => type !== 'sqlite',
        then: Yup.number().required('Required'),
      }),
      database: Yup.string().when('type', {
        is: (type) => type !== 'sqlite',
        then: Yup.string().required('Required'),
      }),
      username: Yup.string().when('type', {
        is: (type) => type !== 'sqlite',
        then: Yup.string().required('Required'),
      }),
      password: Yup.string(),
      connectionString: Yup.string().when('type', {
        is: (type) => type !== 'sqlite',
        then: Yup.string().when('$isConnectionStringTab', {
          is: true,
          then: Yup.string().required('Connection string is required'),
        }),
      }),
    }),
    onSubmit: handleSubmit,
    validateOnMount: true,
    enableReinitialize: true,
  });

  // Update port default based on selected database type
  useEffect(() => {
    if (formik.values.type === 'postgresql') {
      formik.setFieldValue('port', '5432');
    } else if (formik.values.type === 'mysql') {
      formik.setFieldValue('port', '3306');
    } else if (formik.values.type === 'sqlite') {
      // Reset fields for SQLite
      formik.setFieldValue('host', '');
      formik.setFieldValue('port', '');
      formik.setFieldValue('database', '');
      formik.setFieldValue('username', '');
      formik.setFieldValue('password', '');
    }
  }, [formik.values.type]);

  const generateConnectionString = () => {
    const { type, host, port, database, username, password } = formik.values;
    
    if (type === 'postgresql') {
      return `postgresql://${username}${password ? ':' + password : ''}@${host}:${port}/${database}`;
    } else if (type === 'mysql') {
      return `mysql://${username}${password ? ':' + password : ''}@${host}:${port}/${database}`;
    } else if (type === 'sqlite') {
      return 'sqlite:database.db';
    }
    
    return '';
  };

  const handleGenerateConnectionString = () => {
    const connString = generateConnectionString();
    setConnectionString(connString);
    formik.setFieldValue('connectionString', connString);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '60vh',
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">New Database Connection</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
          sx={{ mb: 2 }}
        >
          <Tab label="Standard Connection" />
          <Tab label="Connection String" />
        </Tabs>

        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="name"
                name="name"
                label="Connection Name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                margin="normal"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl
                fullWidth
                margin="normal"
                size="small"
                error={formik.touched.type && Boolean(formik.errors.type)}
              >
                <InputLabel id="db-type-label">Database Type</InputLabel>
                <Select
                  labelId="db-type-label"
                  id="type"
                  name="type"
                  value={formik.values.type}
                  label="Database Type"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                >
                  <MenuItem value="postgresql">PostgreSQL</MenuItem>
                  <MenuItem value="mysql">MySQL</MenuItem>
                  <MenuItem value="sqlite">SQLite</MenuItem>
                </Select>
                {formik.touched.type && formik.errors.type && (
                  <FormHelperText>{formik.errors.type}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {activeTab === 0 ? (
              // Standard connection form
              <>
                {formik.values.type !== 'sqlite' && (
                  <>
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        id="host"
                        name="host"
                        label="Host"
                        value={formik.values.host}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.host && Boolean(formik.errors.host)}
                        helperText={formik.touched.host && formik.errors.host}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={2}>
                      <TextField
                        fullWidth
                        id="port"
                        name="port"
                        label="Port"
                        type="number"
                        value={formik.values.port}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.port && Boolean(formik.errors.port)}
                        helperText={formik.touched.port && formik.errors.port}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={6}>
                      <TextField
                        fullWidth
                        id="database"
                        name="database"
                        label="Database Name"
                        value={formik.values.database}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.database && Boolean(formik.errors.database)}
                        helperText={formik.touched.database && formik.errors.database}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={6}>
                      <TextField
                        fullWidth
                        id="username"
                        name="username"
                        label="Username"
                        value={formik.values.username}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.username && Boolean(formik.errors.username)}
                        helperText={formik.touched.username && formik.errors.username}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={6}>
                      <TextField
                        fullWidth
                        id="password"
                        name="password"
                        label="Password"
                        type={showPassword ? 'text' : 'password'}
                        value={formik.values.password}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.password && Boolean(formik.errors.password)}
                        helperText={formik.touched.password && formik.errors.password}
                        margin="normal"
                        size="small"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle password visibility"
                                onClick={() => setShowPassword(!showPassword)}
                                onMouseDown={(e) => e.preventDefault()}
                                edge="end"
                                size="small"
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                  </>
                )}
                {formik.values.type === 'sqlite' && (
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="database"
                      name="database"
                      label="Database File Path"
                      placeholder="e.g., /path/to/database.db or :memory:"
                      value={formik.values.database}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.database && Boolean(formik.errors.database)}
                      helperText={
                        formik.touched.database && formik.errors.database
                          ? formik.errors.database
                          : 'Enter a file path or use ":memory:" for an in-memory database'
                      }
                      margin="normal"
                      size="small"
                    />
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="flex-end" mt={2}>
                    <Button
                      variant="outlined"
                      onClick={handleGenerateConnectionString}
                      sx={{ mr: 1 }}
                    >
                      Generate Connection String
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleTestConnection}
                      disabled={isConnecting || !formik.isValid}
                      startIcon={
                        isConnecting ? (
                          <CircularProgress size={20} color="inherit" />
                        ) : null
                      }
                    >
                      {isConnecting ? 'Testing...' : 'Test Connection'}
                    </Button>
                  </Box>
                </Grid>
              </>
            ) : (
              // Connection string tab
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  id="connectionString"
                  name="connectionString"
                  label="Connection String"
                  placeholder="e.g., postgresql://username:password@localhost:5432/dbname"
                  value={connectionString}
                  onChange={(e) => {
                    setConnectionString(e.target.value);
                    formik.setFieldValue('connectionString', e.target.value);
                  }}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.connectionString &&
                    Boolean(formik.errors.connectionString)
                  }
                  helperText={
                    formik.touched.connectionString && formik.errors.connectionString
                      ? formik.errors.connectionString
                      : 'Enter a connection string for your database'
                  }
                  margin="normal"
                  size="small"
                />
                <Box display="flex" justifyContent="flex-end" mt={2}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleTestConnection}
                    disabled={isConnecting || !formik.isValid}
                    startIcon={
                      isConnecting ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : null
                    }
                  >
                    {isConnecting ? 'Testing...' : 'Test Connection'}
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>

          {/* Test connection result */}
          <Collapse in={testResult !== null} sx={{ mt: 2 }}>
            {testResult && (
              <Alert
                severity={testResult.success ? 'success' : 'error'}
                icon={
                  testResult.success ? (
                    <CheckCircleIcon fontSize="inherit" />
                  ) : (
                    <ErrorIcon fontSize="inherit" />
                  )
                }
                sx={{ mb: 2 }}
              >
                {testResult.message}
                {testResult.success && testResult.type && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Database type: {testResult.type}
                  </Typography>
                )}
              </Alert>
            )}
          </Collapse>

          {connectionString && (
            <Paper variant="outlined" sx={{ p: 2, mt: 2, backgroundColor: 'background.paper' }}>
              <Typography variant="subtitle2" gutterBottom>
                Generated Connection String:
              </Typography>
              <Box
                component="pre"
                sx={{
                  p: 1,
                  borderRadius: 1,
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  overflow: 'auto',
                  fontSize: '0.75rem',
                  maxHeight: '100px',
                }}
              >
                {connectionString}
              </Box>
            </Paper>
          )}
        </form>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          onClick={formik.handleSubmit}
          disabled={!formik.isValid || isConnecting || !testResult?.success}
          startIcon={
            isConnecting ? <CircularProgress size={20} color="inherit" /> : null
          }
        >
          {isConnecting ? 'Saving...' : 'Save Connection'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConnectionDialog;
