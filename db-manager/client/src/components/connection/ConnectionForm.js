import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  FormHelperText,
  CircularProgress,
  Box,
  Typography,
  IconButton,
  Collapse,
  Alert,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";

// Database type options
const DB_TYPES = [
  { value: "postgresql", label: "PostgreSQL" },
  { value: "mysql", label: "MySQL" },
  { value: "sqlite", label: "SQLite" },
];

// Default ports for database types
const DEFAULT_PORTS = {
  postgresql: "5432",
  mysql: "3306",
  sqlite: "",
};

// Form validation schema
const ConnectionSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  type: Yup.string().required("Database type is required"),
  host: Yup.string().when("type", {
    is: (type) => type !== "sqlite",
    then: (schema) => schema.required("Host is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  port: Yup.number().when("type", {
    is: (type) => type !== "sqlite",
    then: (schema) => schema.required("Port is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  database: Yup.string().when("type", {
    is: (type) => type !== "sqlite",
    then: (schema) => schema.required("Database name is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  username: Yup.string().when("type", {
    is: (type) => type !== "sqlite",
    then: (schema) => schema.required("Username is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  password: Yup.string(),
  filename: Yup.string().when("type", {
    is: "sqlite",
    then: (schema) => schema.required("Database file path is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

const ConnectionForm = ({
  open,
  onClose,
  connection = null,
  onSubmit,
  testConnection,
  isEditing = false,
  loading = false,
  error = null,
}) => {
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  const formik = useFormik({
    initialValues: {
      name: "",
      type: "postgresql",
      host: "localhost",
      port: "5432",
      database: "",
      username: "",
      password: "",
      filename: "",
    },
    validationSchema: ConnectionSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const connectionData = { ...values };

        // Convert port to number
        if (connectionData.port) {
          connectionData.port = parseInt(connectionData.port, 10);
        }

        // Map frontend field names to backend expected names
        if (connectionData.username) {
          connectionData.user = connectionData.username;
          delete connectionData.username;
        }

        await onSubmit(connectionData);
        handleClose();
      } catch (error) {
        console.error("Error saving connection:", error);
      } finally {
        setSubmitting(false);
      }
    },
  });

  // Initialize form with connection data when editing
  useEffect(() => {
    if (connection) {
      formik.setValues({
        ...connection,
        port: connection.port ? connection.port.toString() : "",
      });
    }
  }, [connection, formik]);

  // Update form when database type changes
  useEffect(() => {
    const dbType = formik?.values?.type;
    if (dbType && typeof dbType === "string" && DEFAULT_PORTS[dbType]) {
      const defaultPort = DEFAULT_PORTS[dbType];
      if (formik.values.port !== defaultPort) {
        formik.setFieldValue("port", defaultPort);
      }
    }
  }, [formik.values.type]);

  const handleTestConnection = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const values = formik.values;
      const connectionData = { ...values };

      // Convert port to number for testing
      if (connectionData.port) {
        connectionData.port = parseInt(connectionData.port, 10);
      }

      // Map frontend field names to backend expected names
      if (connectionData.username) {
        connectionData.user = connectionData.username;
        delete connectionData.username;
      }

      const result = await testConnection(connectionData);
      setTestResult({
        success: true,
        message: "Connection successful!",
        type: result?.type || "unknown",
      });
    } catch (error) {
      console.error("Connection test failed:", error);
      setTestResult({
        success: false,
        message:
          error.message || "Connection failed. Please check your settings.",
      });
    } finally {
      setTesting(false);
    }
  };

  const handleClose = () => {
    formik.resetForm();
    setTestResult(null);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      aria-labelledby="connection-form-dialog-title"
    >
      <form onSubmit={formik.handleSubmit}>
        <DialogTitle id="connection-form-dialog-title">
          {isEditing ? "Edit Connection" : "New Connection"}
        </DialogTitle>

        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="name"
                name="name"
                label="Connection Name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                margin="normal"
                required
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl
                fullWidth
                margin="normal"
                required
                error={formik?.touched?.type && Boolean(formik?.errors?.type)}
                disabled={loading}
              >
                <InputLabel id="db-type-label">Database Type</InputLabel>
                <Select
                  labelId="db-type-label"
                  id="type"
                  name="type"
                  value={formik?.values?.type || ""}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Database Type"
                >
                  {DB_TYPES.map((db) => (
                    <MenuItem key={db.value} value={db.value}>
                      {db.label}
                    </MenuItem>
                  ))}
                </Select>
                {formik?.touched?.type && formik?.errors?.type && (
                  <FormHelperText>{formik.errors.type}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {formik?.values?.type && formik.values.type !== "sqlite" ? (
              <>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    id="host"
                    name="host"
                    label="Host"
                    value={formik.values.host}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.host && Boolean(formik.errors.host)}
                    helperText={formik.touched.host && formik.errors.host}
                    margin="normal"
                    required
                    disabled={loading}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="port"
                    name="port"
                    label="Port"
                    type="number"
                    value={formik.values.port}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.port && Boolean(formik.errors.port)}
                    helperText={formik.touched.port && formik.errors.port}
                    margin="normal"
                    required
                    disabled={loading}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="database"
                    name="database"
                    label="Database Name"
                    value={formik.values.database}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.database && Boolean(formik.errors.database)
                    }
                    helperText={
                      formik.touched.database && formik.errors.database
                    }
                    margin="normal"
                    required
                    disabled={loading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="username"
                    name="username"
                    label="Username"
                    value={formik.values.username}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.username && Boolean(formik.errors.username)
                    }
                    helperText={
                      formik.touched.username && formik.errors.username
                    }
                    margin="normal"
                    required
                    disabled={loading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="password"
                    name="password"
                    label="Password"
                    type="password"
                    value={formik.values.password}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.password && Boolean(formik.errors.password)
                    }
                    helperText={
                      formik.touched.password && formik.errors.password
                    }
                    margin="normal"
                    disabled={loading}
                  />
                </Grid>
              </>
            ) : (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="filename"
                  name="filename"
                  label="Database File Path"
                  value={formik.values.filename}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.filename && Boolean(formik.errors.filename)
                  }
                  helperText={
                    formik.touched.filename && formik.errors.filename
                      ? formik.errors.filename
                      : "Absolute path to your SQLite database file"
                  }
                  margin="normal"
                  required
                  disabled={loading}
                />
              </Grid>
            )}
          </Grid>

          <Box mt={2}>
            <Button
              variant="outlined"
              color="primary"
              onClick={handleTestConnection}
              disabled={testing || loading}
              startIcon={
                testing ? <CircularProgress size={20} /> : <CheckCircleIcon />
              }
              fullWidth
            >
              {testing ? "Testing..." : "Test Connection"}
            </Button>

            <Collapse in={testResult !== null}>
              <Box mt={2}>
                <Alert
                  severity={testResult?.success ? "success" : "error"}
                  action={
                    <IconButton
                      aria-label="close"
                      color="inherit"
                      size="small"
                      onClick={() => setTestResult(null)}
                    >
                      <CloseIcon fontSize="inherit" />
                    </IconButton>
                  }
                >
                  <Box display="flex" alignItems="center">
                    {testResult?.success ? (
                      <CheckCircleIcon sx={{ mr: 1 }} />
                    ) : (
                      <ErrorIcon sx={{ mr: 1 }} />
                    )}
                    <Typography variant="body2">
                      {testResult?.message}
                    </Typography>
                  </Box>
                </Alert>
              </Box>
            </Collapse>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={loading || testing}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? "Saving..." : isEditing ? "Update" : "Create"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ConnectionForm;
