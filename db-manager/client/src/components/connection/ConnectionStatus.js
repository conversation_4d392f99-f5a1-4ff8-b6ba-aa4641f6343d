import React from "react";
import { useTheme } from "@mui/material/styles";
import { Box, Tooltip, Typography, Chip, Avatar } from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Storage as DatabaseIcon,
} from "@mui/icons-material";

const ConnectionStatus = ({ connection }) => {
  const theme = useTheme();

  if (!connection) return null;

  const getStatusInfo = () => {
    // In a real app, you would check the actual connection status
    // For now, we'll assume the connection is active if it exists
    return {
      status: "connected",
      message: "Connected",
      icon: <CheckCircleIcon fontSize="small" />,
      color: "success",
    };
  };

  const { status, message, icon, color } = getStatusInfo();

  return (
    <Tooltip
      title={
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Database Connection
          </Typography>
          <Box>
            <Typography variant="caption" display="block">
              <strong>Name:</strong> {connection.name}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>Type:</strong> {connection?.type || "Unknown"}
            </Typography>
            {connection.database && (
              <Typography variant="caption" display="block">
                <strong>Database:</strong> {connection.database}
              </Typography>
            )}
            {connection.host && (
              <Typography variant="caption" display="block">
                <strong>Host:</strong> {connection.host}
                {connection.port && `:${connection.port}`}
              </Typography>
            )}
            <Typography variant="caption" display="block">
              <strong>Status:</strong> {message}
            </Typography>
          </Box>
        </Box>
      }
      arrow
    >
      <Box display="flex" alignItems="center" ml={2}>
        <Chip
          avatar={
            <Avatar
              sx={{
                bgcolor: `${color}.main`,
                color: `${color}.contrastText`,
                width: 24,
                height: 24,
              }}
            >
              {icon}
            </Avatar>
          }
          label={
            <Box display="flex" alignItems="center">
              <DatabaseIcon fontSize="small" sx={{ mr: 0.5 }} />
              <Typography variant="body2" component="span">
                {connection.name}
              </Typography>
            </Box>
          }
          variant="outlined"
          size="small"
          sx={{
            borderColor: `${color}.main`,
            color: "text.primary",
            "& .MuiChip-avatar": {
              color: `${color}.contrastText`,
              bgcolor: `${color}.main`,
            },
          }}
        />
      </Box>
    </Tooltip>
  );
};

export default ConnectionStatus;
