import React, { useState, useMemo } from "react";
import { Outlet, Link, useLocation } from "react-router-dom";
import { styled, useTheme } from "@mui/material/styles";
import {
  Box,
  CssBaseline,
  Toolbar,
  IconButton,
  Typography,
  Drawer,
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  AppBar as MuiAppBar,
  Toolbar as MuiToolbar,
} from "@mui/material";
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Dashboard as DashboardIcon,
  Storage as DatabaseIcon,
  Code as QueryIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import { useTheme as useAppTheme } from "../../contexts/ThemeContext";
import { useConnection } from "../../contexts/ConnectionContext";
import ConnectionDialog from "../connection/ConnectionDialog";
import ConnectionStatus from "../connection/ConnectionStatus";

const drawerWidth = 240;

const Main = styled("main", { shouldForwardProp: (prop) => prop !== "open" })(
  ({ theme, open }) => ({
    flexGrow: 1,
    padding: theme.spacing(3),
    transition: theme.transitions.create("margin", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: 0,
    ...(open && {
      transition: theme.transitions.create("margin", {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
      marginLeft: 0,
    }),
    [theme.breakpoints.up("md")]: {
      marginLeft: `-${drawerWidth}px`,
      ...(open && {
        marginLeft: 0,
      }),
    },
    width: "100%",
    height: "100vh",
    overflow: "auto",
    backgroundColor: theme.palette.background.default,
  })
);

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  transition: theme.transitions.create(["margin", "width"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    width: `calc(100% - ${drawerWidth}px)`,
    marginLeft: `${drawerWidth}px`,
    transition: theme.transitions.create(["margin", "width"], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
  justifyContent: "flex-end",
}));

const MainContent = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  minHeight: "calc(100vh - 64px)",
  padding: theme.spacing(3),
  [theme.breakpoints.down("sm")]: {
    padding: theme.spacing(2),
  },
}));

const Layout = ({ children }) => {
  const theme = useTheme();
  const location = useLocation();
  const { mode, toggleTheme } = useAppTheme();
  const { activeConnection } = useConnection();
  const [open, setOpen] = useState(false);
  const [connectionDialogOpen, setConnectionDialogOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  const handleNewConnection = () => {
    setConnectionDialogOpen(true);
  };

  const navItems = useMemo(
    () => [
      {
        text: "Dashboard",
        icon: <DashboardIcon />,
        path: "/",
      },
      {
        text: "Connections",
        icon: <DatabaseIcon />,
        path: "/connections",
      },
      {
        text: "Query Editor",
        icon: <QueryIcon />,
        path: "/query",
      },
      {
        text: "Settings",
        icon: <SettingsIcon />,
        path: "/settings",
      },
    ],
    []
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar position="fixed" open={open}>
        <MuiToolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={{ mr: 2, ...(open && { display: "none" }) }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Database Manager
          </Typography>
          {activeConnection && (
            <ConnectionStatus connection={activeConnection} />
          )}
          <IconButton color="inherit" onClick={toggleTheme}>
            {mode === "dark" ? "🌞" : "🌙"}
          </IconButton>
        </MuiToolbar>
      </AppBar>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
          },
        }}
        variant={isMobile ? "temporary" : "persistent"}
        anchor="left"
        open={open}
        onClose={handleDrawerClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
      >
        <DrawerHeader>
          <Typography variant="h6" sx={{ flexGrow: 1, ml: 2 }}>
            Navigation
          </Typography>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === "ltr" ? (
              <ChevronLeftIcon />
            ) : (
              <ChevronRightIcon />
            )}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {navItems.map((item) => (
            <ListItem key={item.path} disablePadding>
              <ListItemButton
                component={Link}
                to={item.path}
                selected={location.pathname === item.path}
                onClick={isMobile ? handleDrawerClose : undefined}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
        <Box sx={{ p: 2 }}>
          <ListItemButton
            onClick={handleNewConnection}
            sx={{
              borderRadius: 1,
              backgroundColor: "primary.light",
              color: "primary.contrastText",
              "&:hover": {
                backgroundColor: "primary.dark",
              },
            }}
          >
            <AddIcon sx={{ mr: 1 }} />
            <ListItemText primary="New Connection" />
          </ListItemButton>
        </Box>
      </Drawer>
      <Main open={open}>
        <DrawerHeader />
        <MainContent>{children || <Outlet />}</MainContent>
      </Main>
      <ConnectionDialog
        open={connectionDialogOpen}
        onClose={() => setConnectionDialogOpen(false)}
      />
    </Box>
  );
};

export default Layout;
