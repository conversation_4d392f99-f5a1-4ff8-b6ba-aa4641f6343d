import React, { useState, useMemo } from "react";
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  Alert,
  CircularProgress,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  ToggleButton,
  ToggleButtonGroup,
  Card,
  CardContent,
} from "@mui/material";
import {
  Download as DownloadIcon,
  MoreVert as MoreVertIcon,
  TableChart as TableIcon,
  Assessment as ChartIcon,
  GetApp as ExportIcon,
  ViewList as ListIcon,
  <PERSON><PERSON>hart as BarChartIcon,
} from "@mui/icons-material";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { saveAs } from "file-saver";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

const QueryResults = ({
  data = null,
  loading = false,
  error = null,
  executionTime = null,
  onExport,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [viewMode, setViewMode] = useState("table"); // 'table' or 'chart'

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Prepare data for DataGrid
  const { columns, rows } = useMemo(() => {
    if (!data || !data.rows || data.rows.length === 0) {
      return { columns: [], rows: [] };
    }

    // Create columns from the first row or from fields if available
    let columnDefs = [];

    if (data.fields && data.fields.length > 0) {
      columnDefs = data.fields.map((field, index) => ({
        field: field.name || `col_${index}`,
        headerName: field.name || `Column ${index + 1}`,
        type: getColumnType(field.type),
        width: 150,
        sortable: true,
        filterable: true,
      }));
    } else if (data.rows.length > 0) {
      // Fallback: create columns from first row keys
      const firstRow = data.rows[0];
      columnDefs = Object.keys(firstRow).map((key, index) => ({
        field: key,
        headerName: key,
        type: inferColumnType(firstRow[key]),
        width: 150,
        sortable: true,
        filterable: true,
      }));
    }

    // Prepare rows with unique IDs
    const rowsWithIds = data.rows.map((row, index) => ({
      id: index,
      ...row,
    }));

    return { columns: columnDefs, rows: rowsWithIds };
  }, [data]);

  // Helper function to map database types to DataGrid types
  const getColumnType = (dbType) => {
    if (!dbType) return "string";

    const type = dbType.toLowerCase();
    if (
      type.includes("int") ||
      type.includes("serial") ||
      type.includes("bigint")
    ) {
      return "number";
    }
    if (
      type.includes("float") ||
      type.includes("double") ||
      type.includes("decimal") ||
      type.includes("numeric")
    ) {
      return "number";
    }
    if (type.includes("bool")) {
      return "boolean";
    }
    if (type.includes("date") || type.includes("time")) {
      return "dateTime";
    }
    return "string";
  };

  // Helper function to infer column type from value
  const inferColumnType = (value) => {
    if (value === null || value === undefined) return "string";
    if (typeof value === "number") return "number";
    if (typeof value === "boolean") return "boolean";
    if (value instanceof Date) return "dateTime";
    if (typeof value === "string" && !isNaN(Date.parse(value)))
      return "dateTime";
    return "string";
  };

  // Export functions
  const exportToCSV = () => {
    if (!data || !data.rows) return;

    const csvContent = [
      // Header row
      columns.map((col) => col.headerName).join(","),
      // Data rows
      ...data.rows.map((row) =>
        columns
          .map((col) => {
            const value = row[col.field];
            // Escape commas and quotes in CSV
            if (
              typeof value === "string" &&
              (value.includes(",") || value.includes('"'))
            ) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || "";
          })
          .join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    saveAs(blob, `query_results_${new Date().toISOString().slice(0, 10)}.csv`);
    handleMenuClose();
  };

  const exportToJSON = () => {
    if (!data || !data.rows) return;

    const jsonContent = JSON.stringify(data.rows, null, 2);
    const blob = new Blob([jsonContent], {
      type: "application/json;charset=utf-8;",
    });
    saveAs(blob, `query_results_${new Date().toISOString().slice(0, 10)}.json`);
    handleMenuClose();
  };

  // Prepare data for charts
  const chartData = useMemo(() => {
    if (!data || !data.rows || data.rows.length === 0) return null;

    // Find numeric columns for charts
    const numericColumns = columns.filter((col) => col.type === "number");
    const stringColumns = columns.filter((col) => col.type === "string");

    if (numericColumns.length === 0) return null;

    // For bar chart, use first string column as X-axis and first numeric column as Y-axis
    if (stringColumns.length > 0 && numericColumns.length > 0) {
      const xField = stringColumns[0].field;
      const yField = numericColumns[0].field;

      return data.rows.slice(0, 20).map((row) => ({
        name: row[xField] || "Unknown",
        value: Number(row[yField]) || 0,
      }));
    }

    return null;
  }, [data, columns]);

  const renderChart = () => {
    if (!chartData) {
      return (
        <Box sx={{ p: 4, textAlign: "center" }}>
          <Typography variant="body1" color="textSecondary">
            No suitable data for chart visualization
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Charts require at least one numeric column and one text column
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ height: 400, p: 2 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <RechartsTooltip />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    );
  };

  if (loading) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <CircularProgress size={40} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Executing query...
        </Typography>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Query Error
          </Typography>
          <Typography
            variant="body2"
            component="pre"
            sx={{ whiteSpace: "pre-wrap" }}
          >
            {error}
          </Typography>
        </Alert>
      </Paper>
    );
  }

  if (!data) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <Typography variant="body1" color="textSecondary">
          Execute a query to see results here
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography variant="h6">Query Results</Typography>
          {data.rowCount !== undefined && (
            <Chip
              label={`${data.rowCount} row${data.rowCount !== 1 ? "s" : ""}`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
          {executionTime && (
            <Chip
              label={executionTime}
              size="small"
              color="success"
              variant="outlined"
            />
          )}
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={(event, newMode) => {
              if (newMode !== null) {
                setViewMode(newMode);
              }
            }}
            size="small"
          >
            <ToggleButton value="table" aria-label="table view">
              <Tooltip title="Table View">
                <TableIcon />
              </Tooltip>
            </ToggleButton>
            <ToggleButton value="chart" aria-label="chart view">
              <Tooltip title="Chart View">
                <BarChartIcon />
              </Tooltip>
            </ToggleButton>
          </ToggleButtonGroup>

          <Tooltip title="Export options">
            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Results Content */}
      <Box sx={{ flexGrow: 1, minHeight: 0 }}>
        {rows.length === 0 ? (
          <Box sx={{ p: 4, textAlign: "center" }}>
            <Typography variant="body1" color="textSecondary">
              No data returned
            </Typography>
          </Box>
        ) : viewMode === "table" ? (
          <DataGrid
            rows={rows}
            columns={columns}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 100 },
              },
            }}
            pageSizeOptions={[25, 50, 100, 200]}
            disableRowSelectionOnClick
            slots={{ toolbar: GridToolbar }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                quickFilterProps: { debounceMs: 500 },
              },
            }}
            sx={{
              border: 0,
              "& .MuiDataGrid-cell": {
                borderBottom: 1,
                borderColor: "divider",
              },
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "background.default",
                borderBottom: 2,
                borderColor: "divider",
              },
            }}
          />
        ) : (
          renderChart()
        )}
      </Box>

      {/* Export Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={exportToCSV}>
          <ListItemIcon>
            <ExportIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export as CSV</ListItemText>
        </MenuItem>
        <MenuItem onClick={exportToJSON}>
          <ListItemIcon>
            <ExportIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export as JSON</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default QueryResults;
