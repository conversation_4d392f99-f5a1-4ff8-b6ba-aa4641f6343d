import React, { useState } from "react";
import {
  Box,
  Button,
  IconButton,
  Tooltip,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
} from "@mui/material";
import {
  PlayArrow as ExecuteIcon,
  Stop as StopIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Help as ExplainIcon,
  MoreVert as MoreVertIcon,
  Clear as ClearIcon,
  FormatAlignLeft as FormatIcon,
} from "@mui/icons-material";

const QueryExecutionPanel = ({
  onExecute,
  onStop,
  onSave,
  onClear,
  onFormat,
  onExplain,
  onShowHistory,
  loading = false,
  canExecute = false,
  hasQuery = false,
  connection = null,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleMenuAction = (action) => {
    handleMenuClose();
    action();
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        p: 2,
        borderBottom: 1,
        borderColor: "divider",
        backgroundColor: "background.paper",
      }}
    >
      {/* Left side - Main actions */}
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {loading ? (
          <Button
            variant="contained"
            color="error"
            startIcon={<StopIcon />}
            onClick={onStop}
            size="small"
          >
            Stop
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            startIcon={<ExecuteIcon />}
            onClick={onExecute}
            disabled={!canExecute || !hasQuery}
            size="small"
          >
            Execute
          </Button>
        )}

        <Tooltip title="Explain Query Plan">
          <span>
            <IconButton
              onClick={onExplain}
              disabled={!canExecute || !hasQuery || loading}
              size="small"
            >
              <ExplainIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title="Format SQL">
          <span>
            <IconButton
              onClick={onFormat}
              disabled={!hasQuery || loading}
              size="small"
            >
              <FormatIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        <Tooltip title="Save Query">
          <span>
            <IconButton
              onClick={onSave}
              disabled={!hasQuery || loading}
              size="small"
            >
              <SaveIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title="Query History">
          <IconButton onClick={onShowHistory} size="small">
            <HistoryIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Clear Editor">
          <span>
            <IconButton
              onClick={onClear}
              disabled={!hasQuery || loading}
              size="small"
            >
              <ClearIcon />
            </IconButton>
          </span>
        </Tooltip>
      </Box>

      {/* Right side - Connection info and more options */}
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {connection && (
          <Chip
            label={`${connection?.name || "Unknown"} (${
              connection?.type || "Unknown"
            })`}
            size="small"
            color="primary"
            variant="outlined"
          />
        )}

        <Tooltip title="More options">
          <IconButton onClick={handleMenuOpen} size="small">
            <MoreVertIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* More Options Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem
          onClick={() => handleMenuAction(onFormat)}
          disabled={!hasQuery || loading}
        >
          <ListItemIcon>
            <FormatIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Format SQL</ListItemText>
        </MenuItem>

        <MenuItem
          onClick={() => handleMenuAction(onExplain)}
          disabled={!canExecute || !hasQuery || loading}
        >
          <ListItemIcon>
            <ExplainIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Explain Query Plan</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem
          onClick={() => handleMenuAction(onSave)}
          disabled={!hasQuery || loading}
        >
          <ListItemIcon>
            <SaveIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Save Query</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => handleMenuAction(onShowHistory)}>
          <ListItemIcon>
            <HistoryIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Query History</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem
          onClick={() => handleMenuAction(onClear)}
          disabled={!hasQuery || loading}
        >
          <ListItemIcon>
            <ClearIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Clear Editor</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default QueryExecutionPanel;
