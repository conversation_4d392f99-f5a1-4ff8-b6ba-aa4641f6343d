import React, { useCallback, useRef } from "react";
import { Box, Paper, Typography, useTheme } from "@mui/material";
import CodeMirror from "@uiw/react-codemirror";
import { sql } from "@codemirror/lang-sql";
import { oneDark } from "@codemirror/theme-one-dark";
import { EditorView } from "@codemirror/view";
import { EditorState } from "@codemirror/state";
import { autocompletion } from "@codemirror/autocomplete";

const SqlEditor = ({
  value = "",
  onChange,
  onExecute,
  placeholder = "Enter your SQL query here...",
  readOnly = false,
  height = "300px",
  schema = null,
  loading = false,
}) => {
  const theme = useTheme();
  const editorRef = useRef(null);

  // Create SQL autocompletion based on schema
  const createSqlCompletions = useCallback(() => {
    if (!schema) return [];

    const completions = [];

    // Add SQL keywords
    const sqlKeywords = [
      "SELECT",
      "FROM",
      "WHERE",
      "JOIN",
      "INNER JOIN",
      "LEFT JOIN",
      "RIGHT JOIN",
      "GROUP BY",
      "ORDER BY",
      "HAVING",
      "INSERT",
      "UPDATE",
      "DELETE",
      "CREATE",
      "DROP",
      "ALTER",
      "INDEX",
      "TABLE",
      "VIEW",
      "DATABASE",
      "SCHEMA",
      "PRIMARY KEY",
      "FOREIGN KEY",
      "UNIQUE",
      "NOT NULL",
      "DEFAULT",
      "AND",
      "OR",
      "NOT",
      "IN",
      "EXISTS",
      "BETWEEN",
      "LIKE",
      "IS NULL",
      "COUNT",
      "SUM",
      "AVG",
      "MIN",
      "MAX",
      "DISTINCT",
      "AS",
      "LIMIT",
      "OFFSET",
    ];

    sqlKeywords.forEach((keyword) => {
      completions.push({
        label: keyword,
        type: "keyword",
        info: `SQL keyword: ${keyword}`,
      });
    });

    // Add table names
    if (schema.tables) {
      schema.tables.forEach((table) => {
        completions.push({
          label: table.name,
          type: "table",
          info: `Table: ${table.name}`,
        });

        // Add column names for each table
        if (table.columns) {
          table.columns.forEach((column) => {
            completions.push({
              label: `${table.name}.${column.name}`,
              type: "column",
              info: `Column: ${column.name} (${column.type})`,
            });

            // Also add just the column name
            completions.push({
              label: column.name,
              type: "column",
              info: `Column: ${column.name} (${column.type}) from ${table.name}`,
            });
          });
        }
      });
    }

    return completions;
  }, [schema]);

  // Custom autocompletion function
  const sqlAutocompletion = useCallback(
    (context) => {
      const completions = createSqlCompletions();
      const word = context.matchBefore(/\w*/);

      if (word === null || (word.from === word.to && !context.explicit)) {
        return null;
      }

      return {
        from: word.from,
        options: completions.filter((completion) =>
          completion.label.toLowerCase().includes(word.text.toLowerCase())
        ),
      };
    },
    [createSqlCompletions]
  );

  // Editor extensions
  const extensions = [
    sql(),
    autocompletion({ override: [sqlAutocompletion] }),
    EditorView.theme({
      "&": {
        fontSize: "14px",
        fontFamily: '"Fira Code", "Monaco", "Menlo", monospace',
      },
      ".cm-content": {
        padding: "16px",
        minHeight: height,
      },
      ".cm-focused": {
        outline: "none",
      },
      ".cm-editor": {
        borderRadius: "4px",
      },
      ".cm-scroller": {
        fontFamily: "inherit",
      },
    }),
    EditorView.lineWrapping,
    EditorState.tabSize.of(2),
  ];

  // Add dark theme if needed
  if (theme.palette.mode === "dark") {
    extensions.push(oneDark);
  }

  // Handle key bindings
  const handleKeyDown = useCallback(
    (event) => {
      // Ctrl+Enter or Cmd+Enter to execute query
      if ((event.ctrlKey || event.metaKey) && event.key === "Enter") {
        event.preventDefault();
        if (onExecute && !loading) {
          onExecute();
        }
      }
    },
    [onExecute, loading]
  );

  return (
    <Paper
      elevation={1}
      sx={{
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 1,
        overflow: "hidden",
        "& .cm-editor": {
          backgroundColor: theme.palette.background.paper,
        },
        "& .cm-content": {
          color: theme.palette.text.primary,
        },
        "& .cm-cursor": {
          borderLeftColor: theme.palette.primary.main,
        },
        "& .cm-selectionBackground": {
          backgroundColor: theme.palette.action.selected,
        },
      }}
    >
      <Box
        sx={{
          borderBottom: `1px solid ${theme.palette.divider}`,
          px: 2,
          py: 1,
          backgroundColor: theme.palette.background.default,
        }}
      >
        <Typography variant="body2" color="textSecondary">
          SQL Editor {onExecute && "(Ctrl+Enter to execute)"}
        </Typography>
      </Box>

      <Box onKeyDown={handleKeyDown}>
        <CodeMirror
          ref={editorRef}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          editable={!readOnly}
          extensions={extensions}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            dropCursor: false,
            allowMultipleSelections: false,
            indentOnInput: true,
            bracketMatching: true,
            closeBrackets: true,
            autocompletion: true,
            highlightSelectionMatches: true,
            searchKeymap: true,
            historyKeymap: true,
            defaultKeymap: true,
          }}
        />
      </Box>
    </Paper>
  );
};

export default SqlEditor;
