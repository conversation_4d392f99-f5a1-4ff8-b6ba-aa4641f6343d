import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  Chip,
  TextField,
  InputAdornment,
  Divider,
  Paper,
  Alert,
} from "@mui/material";
import {
  Search as SearchIcon,
  Delete as DeleteIcon,
  PlayArrow as ExecuteIcon,
  ContentCopy as CopyIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from "@mui/icons-material";
import { formatDistanceToNow } from "date-fns";

const QueryHistory = ({ open, onClose, onSelectQuery, connectionId }) => {
  const [history, setHistory] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState(null);

  useEffect(() => {
    if (open && connectionId) {
      fetchHistory();
    }
  }, [open, connectionId]);

  useEffect(() => {
    // Filter history based on search term
    if (searchTerm.trim() === "") {
      setFilteredHistory(history);
    } else {
      const filtered = history.filter(
        (item) =>
          item.query.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredHistory(filtered);
    }
  }, [history, searchTerm]);

  const fetchHistory = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/query/history/${connectionId}`);
      if (response.ok) {
        const data = await response.json();
        const historyWithFavorites = data.data.map((item) => ({
          ...item,
          executedAt: new Date(item.executedAt),
          isFavorite: false, // In a real app, this would come from user preferences
        }));
        setHistory(historyWithFavorites);
      } else {
        console.error("Failed to fetch query history");
        setHistory([]);
      }
    } catch (error) {
      console.error("Failed to fetch query history:", error);
      setHistory([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectQuery = (queryItem) => {
    setSelectedQuery(queryItem);
  };

  const handleExecuteQuery = (queryItem) => {
    onSelectQuery(queryItem.query);
    onClose();
  };

  const handleCopyQuery = async (query) => {
    try {
      await navigator.clipboard.writeText(query);
      // Show success message (you could use a snackbar here)
    } catch (error) {
      console.error("Failed to copy query:", error);
    }
  };

  const handleToggleFavorite = (queryId) => {
    setHistory((prev) =>
      prev.map((item) =>
        item.id === queryId ? { ...item, isFavorite: !item.isFavorite } : item
      )
    );
  };

  const handleDeleteQuery = (queryId) => {
    setHistory((prev) => prev.filter((item) => item.id !== queryId));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return "success";
      case "error":
        return "error";
      case "warning":
        return "warning";
      default:
        return "default";
    }
  };

  const truncateQuery = (query, maxLength = 100) => {
    if (query.length <= maxLength) return query;
    return query.substring(0, maxLength) + "...";
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Query History</DialogTitle>

      <DialogContent>
        {/* Search */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search queries..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* History List */}
        <Box sx={{ display: "flex", gap: 2, height: "400px" }}>
          {/* Query List */}
          <Box sx={{ flex: 1, overflow: "auto" }}>
            {loading ? (
              <Typography>Loading history...</Typography>
            ) : filteredHistory.length === 0 ? (
              <Alert severity="info">
                {searchTerm
                  ? "No queries match your search"
                  : "No query history found"}
              </Alert>
            ) : (
              <List dense>
                {filteredHistory.map((item) => (
                  <ListItem key={item.id} disablePadding>
                    <ListItemButton
                      onClick={() => handleSelectQuery(item)}
                      selected={selectedQuery?.id === item.id}
                    >
                      <ListItemText
                        primary={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Typography variant="body2" noWrap>
                              {item.name || truncateQuery(item.query, 50)}
                            </Typography>
                            <Chip
                              label={item.status}
                              size="small"
                              color={getStatusColor(item.status)}
                              variant="outlined"
                            />
                            {item.isFavorite && (
                              <StarIcon color="warning" fontSize="small" />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              {formatDistanceToNow(item.executedAt, {
                                addSuffix: true,
                              })}
                            </Typography>
                            {item.executionTime && (
                              <Typography
                                variant="caption"
                                color="textSecondary"
                                sx={{ ml: 1 }}
                              >
                                • {item.executionTime}
                              </Typography>
                            )}
                            {item.rowCount !== null && (
                              <Typography
                                variant="caption"
                                color="textSecondary"
                                sx={{ ml: 1 }}
                              >
                                • {item.rowCount} rows
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleFavorite(item.id);
                          }}
                        >
                          {item.isFavorite ? (
                            <StarIcon color="warning" />
                          ) : (
                            <StarBorderIcon />
                          )}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCopyQuery(item.query);
                          }}
                        >
                          <CopyIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExecuteQuery(item);
                          }}
                        >
                          <ExecuteIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteQuery(item.id);
                          }}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>

          {/* Query Preview */}
          {selectedQuery && (
            <>
              <Divider orientation="vertical" flexItem />
              <Box sx={{ flex: 1, overflow: "auto" }}>
                <Typography variant="h6" gutterBottom>
                  Query Preview
                </Typography>
                <Paper sx={{ p: 2, mb: 2, backgroundColor: "grey.50" }}>
                  <Typography
                    component="pre"
                    variant="body2"
                    sx={{
                      fontFamily: "monospace",
                      whiteSpace: "pre-wrap",
                      margin: 0,
                      fontSize: "12px",
                    }}
                  >
                    {selectedQuery.query}
                  </Typography>
                </Paper>

                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                  <Button
                    size="small"
                    variant="contained"
                    startIcon={<ExecuteIcon />}
                    onClick={() => handleExecuteQuery(selectedQuery)}
                  >
                    Execute
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<CopyIcon />}
                    onClick={() => handleCopyQuery(selectedQuery.query)}
                  >
                    Copy
                  </Button>
                </Box>
              </Box>
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default QueryHistory;
