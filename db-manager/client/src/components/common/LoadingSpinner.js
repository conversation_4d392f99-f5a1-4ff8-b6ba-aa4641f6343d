import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const LoadingSpinner = ({ message = 'Loading...', size = 40, thickness = 4 }) => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="200px"
      width="100%"
      p={4}
    >
      <CircularProgress 
        size={size} 
        thickness={thickness}
        sx={{
          color: 'primary.main',
          mb: 2,
        }}
      />
      {message && (
        <Typography 
          variant="body1" 
          color="textSecondary"
          sx={{ mt: 2 }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

export default LoadingSpinner;
