import React from "react";
import { Box, Typography, Button, Paper, Alert } from "@mui/material";
import { Refresh as RefreshIcon } from "@mui/icons-material";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console for debugging
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    // Log additional details for debugging branch-related errors
    if (error.message && error.message.includes("branch")) {
      console.error("Branch-related error detected:", {
        message: error.message,
        stack: error.stack,
        errorInfo,
        userAgent: navigator.userAgent,
        url: window.location.href,
      });
    }

    // Update state with error details
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }

  handleReload = () => {
    // Reset the error state and reload the page
    this.setState({ hasError: false, error: null, errorInfo: null });
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            minHeight: "100vh",
            p: 3,
            bgcolor: "background.default",
          }}
        >
          <Paper
            elevation={3}
            sx={{
              p: 4,
              maxWidth: 600,
              width: "100%",
              textAlign: "center",
            }}
          >
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Something went wrong
              </Typography>
              <Typography variant="body2">
                An unexpected error occurred in the application. This might be
                due to a browser extension or a temporary issue.
              </Typography>
            </Alert>

            <Typography variant="h6" gutterBottom>
              Troubleshooting Steps:
            </Typography>

            <Box sx={{ textAlign: "left", mb: 3 }}>
              <Typography variant="body2" component="div" sx={{ mb: 1 }}>
                1. Try refreshing the page using the button below
              </Typography>
              <Typography variant="body2" component="div" sx={{ mb: 1 }}>
                2. If the error persists, try opening the app in an
                incognito/private window
              </Typography>
              <Typography variant="body2" component="div" sx={{ mb: 1 }}>
                3. Disable browser extensions temporarily
              </Typography>
              <Typography variant="body2" component="div">
                4. Clear your browser cache and cookies
              </Typography>
            </Box>

            <Button
              variant="contained"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={this.handleReload}
              size="large"
            >
              Reload Application
            </Button>

            {process.env.NODE_ENV === "development" && this.state.error && (
              <Box sx={{ mt: 3, textAlign: "left" }}>
                <Typography variant="subtitle2" gutterBottom>
                  Error Details (Development Mode):
                </Typography>
                <Paper
                  sx={{
                    p: 2,
                    bgcolor: "grey.100",
                    fontFamily: "monospace",
                    fontSize: "0.875rem",
                    maxHeight: 200,
                    overflow: "auto",
                  }}
                >
                  <Typography variant="body2" component="pre">
                    {this.state.error.toString()}
                    {this.state.errorInfo.componentStack}
                  </Typography>
                </Paper>
              </Box>
            )}
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
