import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  <PERSON>lapse,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Chip,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  TableChart as TableIcon,
  ViewList as ViewIcon,
  Storage as DatabaseIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewDataIcon,
} from "@mui/icons-material";
import { useConnection } from "../../contexts/ConnectionContext";
import TableManager from "./TableManager";

const SchemaBrowser = ({ onTableSelect, onRefresh, selectedTable = null }) => {
  const { activeConnection } = useConnection();
  const [schema, setSchema] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedTables, setExpandedTables] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [tableManagerOpen, setTableManagerOpen] = useState(false);
  const [editingTable, setEditingTable] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);

  // Fetch schema data
  const fetchSchema = useCallback(async () => {
    if (!activeConnection) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/connections/${activeConnection.id}/schema`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch schema");
      }
      const data = await response.json();
      setSchema(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [activeConnection]);

  useEffect(() => {
    fetchSchema();
  }, [fetchSchema]);

  const handleTableToggle = (tableName) => {
    const newExpanded = new Set(expandedTables);
    if (newExpanded.has(tableName)) {
      newExpanded.delete(tableName);
    } else {
      newExpanded.add(tableName);
    }
    setExpandedTables(newExpanded);
  };

  const handleTableSelect = (table) => {
    if (onTableSelect) {
      onTableSelect(table);
    }
  };

  const handleRefresh = () => {
    fetchSchema();
    if (onRefresh) {
      onRefresh();
    }
  };

  // Filter tables based on search term
  const filteredTables =
    schema?.tables?.filter((table) =>
      table.name.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  const getTableIcon = (table) => {
    return table.type === "view" ? <ViewIcon /> : <TableIcon />;
  };

  const getColumnTypeColor = (type) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes("int") || lowerType.includes("serial"))
      return "primary";
    if (
      lowerType.includes("varchar") ||
      lowerType.includes("text") ||
      lowerType.includes("char")
    )
      return "secondary";
    if (lowerType.includes("date") || lowerType.includes("time"))
      return "success";
    if (lowerType.includes("bool")) return "warning";
    if (
      lowerType.includes("float") ||
      lowerType.includes("double") ||
      lowerType.includes("decimal")
    )
      return "info";
    return "default";
  };

  const handleContextMenu = (event, table) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      table: table,
    });
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  const handleCreateTable = () => {
    setEditingTable(null);
    setTableManagerOpen(true);
  };

  const handleEditTable = (table) => {
    setEditingTable(table);
    setTableManagerOpen(true);
    handleContextMenuClose();
  };

  const handleDeleteTable = async (table) => {
    if (
      window.confirm(`Are you sure you want to delete table "${table.name}"?`)
    ) {
      try {
        const response = await fetch(
          `/api/connections/${activeConnection.id}/tables/${table.name}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          fetchSchema(); // Refresh schema
        } else {
          alert("Failed to delete table");
        }
      } catch (error) {
        alert("Error deleting table: " + error.message);
      }
    }
    handleContextMenuClose();
  };

  const handleViewTableData = (table) => {
    handleTableSelect(table);
    handleContextMenuClose();
  };

  const handleSaveTable = async (tableData) => {
    try {
      const url = editingTable
        ? `/api/connections/${activeConnection.id}/tables/${editingTable.name}`
        : `/api/connections/${activeConnection.id}/tables`;

      const method = editingTable ? "PUT" : "POST";

      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(tableData),
      });

      if (response.ok) {
        setTableManagerOpen(false);
        setEditingTable(null);
        fetchSchema(); // Refresh schema
      } else {
        const error = await response.json();
        alert("Failed to save table: " + (error.message || "Unknown error"));
      }
    } catch (error) {
      alert("Error saving table: " + error.message);
    }
  };

  if (!activeConnection) {
    return (
      <Paper sx={{ p: 3, textAlign: "center" }}>
        <DatabaseIcon sx={{ fontSize: 48, color: "text.secondary", mb: 2 }} />
        <Typography variant="h6" color="textSecondary">
          No Connection Selected
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Select a database connection to browse its schema
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="h6">Database Schema</Typography>
        <Box>
          <Tooltip title="Create Table">
            <IconButton
              onClick={handleCreateTable}
              disabled={loading}
              size="small"
            >
              <AddIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh Schema">
            <IconButton onClick={handleRefresh} disabled={loading} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Search */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
        <TextField
          fullWidth
          size="small"
          placeholder="Search tables..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Content */}
      <Box sx={{ flexGrow: 1, overflow: "auto" }}>
        {loading && (
          <Box sx={{ p: 3, textAlign: "center" }}>
            <CircularProgress size={32} />
            <Typography variant="body2" sx={{ mt: 1 }}>
              Loading schema...
            </Typography>
          </Box>
        )}

        {error && (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}

        {schema && !loading && (
          <List dense>
            {filteredTables.length === 0 ? (
              <ListItem>
                <ListItemText
                  primary="No tables found"
                  secondary={
                    searchTerm
                      ? "Try adjusting your search term"
                      : "This database appears to be empty"
                  }
                />
              </ListItem>
            ) : (
              filteredTables.map((table) => (
                <Box key={table.name}>
                  <ListItemButton
                    onClick={() => handleTableToggle(table.name)}
                    onContextMenu={(e) => handleContextMenu(e, table)}
                    selected={selectedTable === table.name}
                    sx={{ pl: 1 }}
                  >
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      {expandedTables.has(table.name) ? (
                        <ExpandMoreIcon />
                      ) : (
                        <ChevronRightIcon />
                      )}
                    </ListItemIcon>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      {getTableIcon(table)}
                    </ListItemIcon>
                    <ListItemText
                      primary={table.name}
                      secondary={table.schema && `Schema: ${table.schema}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTableSelect(table);
                      }}
                    />
                    {table.columns && (
                      <Chip
                        label={table.columns.length}
                        size="small"
                        variant="outlined"
                        sx={{ ml: 1 }}
                      />
                    )}
                  </ListItemButton>

                  <Collapse
                    in={expandedTables.has(table.name)}
                    timeout="auto"
                    unmountOnExit
                  >
                    <List component="div" disablePadding dense>
                      {table.columns?.map((column) => (
                        <ListItem key={column.name} sx={{ pl: 8 }}>
                          <ListItemText
                            primary={
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Typography variant="body2" component="span">
                                  {column.name}
                                </Typography>
                                <Chip
                                  label={column.type}
                                  size="small"
                                  color={getColumnTypeColor(column.type)}
                                  variant="outlined"
                                />
                                {column.isNullable === false && (
                                  <Chip
                                    label="NOT NULL"
                                    size="small"
                                    color="error"
                                    variant="outlined"
                                  />
                                )}
                                {column.defaultValue && (
                                  <Chip
                                    label={`Default: ${column.defaultValue}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Collapse>
                </Box>
              ))
            )}
          </List>
        )}
      </Box>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={() => handleViewTableData(contextMenu?.table)}>
          <ListItemIcon>
            <ViewDataIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Data</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleEditTable(contextMenu?.table)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Table</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleDeleteTable(contextMenu?.table)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Delete Table</ListItemText>
        </MenuItem>
      </Menu>

      {/* Table Manager Dialog */}
      <TableManager
        open={tableManagerOpen}
        onClose={() => {
          setTableManagerOpen(false);
          setEditingTable(null);
        }}
        onSave={handleSaveTable}
        table={editingTable}
        databaseType={activeConnection?.type}
        isEditing={!!editingTable}
      />
    </Paper>
  );
};

export default SchemaBrowser;
