import React, { useState } from "react";
import {
  <PERSON>alog,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Tooltip,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
} from "@mui/icons-material";

const TableManager = ({
  open,
  onClose,
  onSave,
  table = null,
  databaseType = "postgresql",
  isEditing = false,
}) => {
  const [tableName, setTableName] = useState(table?.name || "");
  const [columns, setColumns] = useState(
    table?.columns || [
      {
        name: "id",
        type: "INTEGER",
        isPrimaryKey: true,
        isNotNull: true,
        defaultValue: "",
      },
    ]
  );

  // Database type specific column types
  const getColumnTypes = () => {
    switch (databaseType) {
      case "postgresql":
        return [
          "INTEGER",
          "BIGINT",
          "SERIAL",
          "BIGSERIAL",
          "VARCHAR",
          "TEXT",
          "CHAR",
          "BOOLEAN",
          "DATE",
          "TIMESTAMP",
          "TIME",
          "DECIMAL",
          "NUMERIC",
          "REAL",
          "DOUBLE PRECISION",
          "JSON",
          "JSONB",
          "UUID",
        ];
      case "mysql":
        return [
          "INT",
          "BIGINT",
          "TINYINT",
          "SMALLINT",
          "MEDIUMINT",
          "VARCHAR",
          "TEXT",
          "CHAR",
          "LONGTEXT",
          "MEDIUMTEXT",
          "BOOLEAN",
          "BOOL",
          "DATE",
          "DATETIME",
          "TIMESTAMP",
          "TIME",
          "DECIMAL",
          "FLOAT",
          "DOUBLE",
          "JSON",
        ];
      case "sqlite":
        return ["INTEGER", "TEXT", "REAL", "BLOB", "NUMERIC"];
      default:
        return ["VARCHAR", "INTEGER", "TEXT", "DATE", "BOOLEAN"];
    }
  };

  const addColumn = () => {
    setColumns([
      ...columns,
      {
        name: "",
        type: "VARCHAR",
        length: "",
        isPrimaryKey: false,
        isNotNull: false,
        defaultValue: "",
      },
    ]);
  };

  const removeColumn = (index) => {
    setColumns(columns.filter((_, i) => i !== index));
  };

  const updateColumn = (index, field, value) => {
    const newColumns = [...columns];
    newColumns[index] = { ...newColumns[index], [field]: value };
    setColumns(newColumns);
  };

  const generateCreateTableSQL = () => {
    if (!tableName || columns.length === 0) return "";

    let sql = `CREATE TABLE ${tableName} (\n`;

    const columnDefinitions = columns.map((col) => {
      let def = `  ${col.name} ${col.type}`;

      // Add length for VARCHAR types
      if (col.type === "VARCHAR" && col.length) {
        def += `(${col.length})`;
      }

      // Add constraints
      if (col.isPrimaryKey) {
        def += " PRIMARY KEY";
      }
      if (col.isNotNull && !col.isPrimaryKey) {
        def += " NOT NULL";
      }
      if (col.defaultValue) {
        def += ` DEFAULT ${col.defaultValue}`;
      }

      return def;
    });

    sql += columnDefinitions.join(",\n");
    sql += "\n);";

    return sql;
  };

  const handleSave = () => {
    if (!tableName.trim()) {
      alert("Please enter a table name");
      return;
    }

    if (columns.length === 0) {
      alert("Please add at least one column");
      return;
    }

    const hasInvalidColumns = columns.some(
      (col) => !col.name.trim() || !col.type
    );
    if (hasInvalidColumns) {
      alert("Please ensure all columns have names and types");
      return;
    }

    const tableData = {
      name: tableName,
      columns: columns,
      sql: generateCreateTableSQL(),
    };

    onSave(tableData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {isEditing ? `Edit Table: ${table?.name}` : "Create New Table"}
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="Table Name"
            value={tableName}
            onChange={(e) => setTableName(e.target.value)}
            margin="normal"
            required
            disabled={isEditing}
          />
        </Box>

        <Box
          sx={{
            mb: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6">Columns</Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={addColumn}
            variant="outlined"
            size="small"
          >
            Add Column
          </Button>
        </Box>

        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Length</TableCell>
                <TableCell>Primary Key</TableCell>
                <TableCell>Not Null</TableCell>
                <TableCell>Default</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {columns.map((column, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <TextField
                      size="small"
                      value={column.name}
                      onChange={(e) =>
                        updateColumn(index, "name", e.target.value)
                      }
                      placeholder="Column name"
                      required
                    />
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select
                        value={column.type}
                        onChange={(e) =>
                          updateColumn(index, "type", e.target.value)
                        }
                      >
                        {getColumnTypes().map((type) => (
                          <MenuItem key={type} value={type}>
                            {type}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <TextField
                      size="small"
                      value={column.length || ""}
                      onChange={(e) =>
                        updateColumn(index, "length", e.target.value)
                      }
                      placeholder="Length"
                      type="number"
                      disabled={!["VARCHAR", "CHAR"].includes(column.type)}
                    />
                  </TableCell>
                  <TableCell>
                    <Checkbox
                      checked={column.isPrimaryKey || false}
                      onChange={(e) =>
                        updateColumn(index, "isPrimaryKey", e.target.checked)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <Checkbox
                      checked={column.isNotNull || false}
                      onChange={(e) =>
                        updateColumn(index, "isNotNull", e.target.checked)
                      }
                      disabled={column.isPrimaryKey}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      size="small"
                      value={column.defaultValue || ""}
                      onChange={(e) =>
                        updateColumn(index, "defaultValue", e.target.value)
                      }
                      placeholder="Default value"
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Remove column">
                      <IconButton
                        size="small"
                        onClick={() => removeColumn(index)}
                        color="error"
                        disabled={columns.length === 1}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* SQL Preview */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            SQL Preview
          </Typography>
          <Paper sx={{ p: 2, backgroundColor: "grey.100" }}>
            <Typography
              component="pre"
              variant="body2"
              sx={{
                fontFamily: "monospace",
                whiteSpace: "pre-wrap",
                margin: 0,
              }}
            >
              {generateCreateTableSQL()}
            </Typography>
          </Paper>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          startIcon={<SaveIcon />}
          disabled={!tableName.trim() || columns.length === 0}
        >
          {isEditing ? "Update Table" : "Create Table"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TableManager;
