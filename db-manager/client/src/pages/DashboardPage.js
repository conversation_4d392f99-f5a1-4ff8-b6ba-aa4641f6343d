import React from "react";
import { Box, Typography, Grid, Paper, Button } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useConnection } from "../contexts/ConnectionContext";
import {
  Storage as DatabaseIcon,
  Add as AddIcon,
  Code as CodeIcon,
} from "@mui/icons-material";

const DashboardPage = () => {
  const navigate = useNavigate();
  const { connections, activeConnection } = useConnection();

  // Debug logging (development only)
  if (process.env.NODE_ENV === "development") {
    console.log("DashboardPage - connections:", connections);
    console.log("DashboardPage - connections type:", typeof connections);
    console.log(
      "DashboardPage - connections isArray:",
      Array.isArray(connections)
    );
  }

  // Ensure connections is an array before using array methods
  const safeConnections = Array.isArray(connections) ? connections : [];
  const recentConnections = safeConnections.slice(0, 3);
  const recentQueries = []; // We'll implement this later

  return (
    <Box>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Welcome to Database Manager. Connect to your databases and manage your
          data with ease.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: "100%" }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box mt={2} display="flex" flexDirection="column" gap={2}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => navigate("/connections")}
                fullWidth
              >
                New Connection
              </Button>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<CodeIcon />}
                onClick={() => navigate("/query")}
                fullWidth
                disabled={!activeConnection}
              >
                New Query
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Recent Connections */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: "100%" }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={2}
            >
              <Typography variant="h6">Recent Connections</Typography>
              {safeConnections.length > 0 && (
                <Button size="small" onClick={() => navigate("/connections")}>
                  View All
                </Button>
              )}
            </Box>
            {recentConnections.length > 0 ? (
              <Box>
                {recentConnections.map((connection) => (
                  <Box
                    key={connection.id}
                    sx={{
                      p: 2,
                      mb: 1,
                      borderRadius: 1,
                      bgcolor: "background.paper",
                      border: "1px solid",
                      borderColor: "divider",
                      display: "flex",
                      alignItems: "center",
                      cursor: "pointer",
                      "&:hover": {
                        bgcolor: "action.hover",
                      },
                    }}
                    onClick={() => {
                      // Set as active connection and navigate to query
                      // This would be implemented with your connection context
                      // setActiveConnection(connection);
                      navigate(`/query/${connection.id}`);
                    }}
                  >
                    <DatabaseIcon
                      sx={{
                        mr: 2,
                        color: "primary.main",
                      }}
                    />
                    <Box>
                      <Typography variant="subtitle2">
                        {connection.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {connection?.type || "Unknown"} •{" "}
                        {connection?.host || "localhost"}
                        {connection.port ? `:${connection.port}` : ""}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            ) : (
              <Box
                sx={{
                  p: 3,
                  textAlign: "center",
                  color: "text.secondary",
                }}
              >
                <Typography variant="body2">
                  No recent connections. Create a new connection to get started.
                </Typography>
                <Button
                  variant="text"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => navigate("/connections")}
                  sx={{ mt: 1 }}
                >
                  Add Connection
                </Button>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Recent Queries */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: "100%" }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={2}
            >
              <Typography variant="h6">Recent Queries</Typography>
              {recentQueries.length > 0 && (
                <Button size="small" onClick={() => navigate("/query/history")}>
                  View All
                </Button>
              )}
            </Box>
            {recentQueries.length > 0 ? (
              <Box>
                {recentQueries.map((query, index) => (
                  <Box
                    key={index}
                    sx={{
                      p: 2,
                      mb: 1,
                      borderRadius: 1,
                      bgcolor: "background.paper",
                      border: "1px solid",
                      borderColor: "divider",
                      cursor: "pointer",
                      "&:hover": {
                        bgcolor: "action.hover",
                      },
                    }}
                    onClick={() => {
                      // Navigate to query editor with the query
                      navigate("/query", { state: { query } });
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {query.name || "Untitled Query"}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="textSecondary"
                      sx={{
                        display: "block",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {query.sql || "No SQL content"}
                    </Typography>
                  </Box>
                ))}
              </Box>
            ) : (
              <Box
                sx={{
                  p: 3,
                  textAlign: "center",
                  color: "text.secondary",
                }}
              >
                <Typography variant="body2">
                  Your recent queries will appear here.
                </Typography>
                <Button
                  variant="text"
                  size="small"
                  startIcon={<CodeIcon />}
                  onClick={() => navigate("/query")}
                  disabled={!activeConnection}
                  sx={{ mt: 1 }}
                >
                  New Query
                </Button>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
