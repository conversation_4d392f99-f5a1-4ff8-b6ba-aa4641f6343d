import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardActionArea,
  Snackbar,
  Alert,
  Tooltip,
  IconButton,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Storage as DatabaseIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { useConnection } from "../contexts/ConnectionContext";
import LoadingSpinner from "../components/common/LoadingSpinner";
import ConnectionForm from "../components/connection/ConnectionForm";

// Get database icon with color based on type
const getDbIcon = (type, color = "inherit") => {
  const iconStyle = { fontSize: 20, color };
  if (!type) return <DatabaseIcon style={iconStyle} />;

  switch (type) {
    case "postgresql":
      return <DatabaseIcon style={{ ...iconStyle, color: "#336791" }} />;
    case "mysql":
      return <DatabaseIcon style={{ ...iconStyle, color: "#4479A1" }} />;
    case "sqlite":
      return <DatabaseIcon style={{ ...iconStyle, color: "#003B57" }} />;
    default:
      return <DatabaseIcon style={iconStyle} />;
  }
};

const ConnectionsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConnection, setEditingConnection] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const {
    connections,
    activeConnection,
    setActiveConnection,
    createConnection,
    updateConnection,
    deleteConnection,
    testConnection,
    loading,
    error,
  } = useConnection();

  // Show success/error message from navigation state
  useEffect(() => {
    if (location.state?.message) {
      setSnackbar({
        open: true,
        message: location.state.message,
        severity: "success",
      });
      // Clear the state to prevent showing the message again on refresh
      window.history.replaceState({}, document.title);
    } else if (location.state?.error) {
      setSnackbar({
        open: true,
        message: location.state.error,
        severity: "error",
      });
      window.history.replaceState({}, document.title);
    }
  }, [location]);

  const handleOpenDialog = (connection = null) => {
    setEditingConnection(connection);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingConnection(null);
  };

  const handleSubmitConnection = async (connectionData) => {
    try {
      if (editingConnection) {
        await updateConnection(editingConnection.id, connectionData);
        setSnackbar({
          open: true,
          message: "Connection updated successfully",
          severity: "success",
        });
      } else {
        await createConnection(connectionData);
        setSnackbar({
          open: true,
          message: "Connection created successfully",
          severity: "success",
        });
      }
      handleCloseDialog();
    } catch (error) {
      console.error("Error saving connection:", error);
      setSnackbar({
        open: true,
        message: `Failed to save connection: ${error.message}`,
        severity: "error",
      });
      throw error; // Re-throw to let the form handle the error
    }
  };

  const handleCloseSnackbar = (_, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const handleSetActiveConnection = (connection) => {
    setActiveConnection(connection);
  };

  const handleConnect = (connection) => {
    setActiveConnection(connection);
    navigate("/query");
  };

  const handleDeleteConnection = async (id, event) => {
    event.stopPropagation();
    if (window.confirm("Are you sure you want to delete this connection?")) {
      try {
        await deleteConnection(id);
        setSnackbar({
          open: true,
          message: "Connection deleted successfully",
          severity: "success",
        });
      } catch (error) {
        console.error("Error deleting connection:", error);
        setSnackbar({
          open: true,
          message: `Failed to delete connection: ${error.message}`,
          severity: "error",
        });
      }
    }
  };

  if (loading && !connections.length) {
    return <LoadingSpinner message="Loading connections..." />;
  }

  if (error) {
    return (
      <Box p={3} textAlign="center">
        <Typography color="error" gutterBottom>
          Error loading connections: {error.message}
        </Typography>
        <Button
          variant="outlined"
          color="primary"
          onClick={() => window.location.reload()}
          startIcon={<RefreshIcon />}
        >
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box
        mb={4}
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Typography variant="h4" component="h1">
          Database Connections
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          New Connection
        </Button>
      </Box>

      {connections.length === 0 ? (
        <Box textAlign="center" p={4}>
          <Box maxWidth={500} mx="auto">
            <Box fontSize={48} color="text.secondary" mb={2}>
              <DatabaseIcon fontSize="inherit" />
            </Box>
            <Typography variant="h6" gutterBottom>
              No connections found
            </Typography>
            <Typography color="textSecondary" sx={{ mb: 2 }}>
              Get started by adding your first database connection to manage
              your data.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              size="large"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              Add Connection
            </Button>
          </Box>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {connections
            .filter((connection) => connection && connection.id)
            .map((connection) => (
              <Grid item xs={12} sm={6} md={4} key={connection.id}>
                <Card
                  elevation={0}
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    border: "1px solid",
                    borderColor:
                      activeConnection?.id === connection.id
                        ? "primary.main"
                        : "divider",
                    "&:hover": {
                      boxShadow: 3,
                    },
                  }}
                >
                  <CardActionArea
                    onClick={() => handleSetActiveConnection(connection)}
                    sx={{ flexGrow: 1, p: 2 }}
                  >
                    <Box display="flex" alignItems="center" mb={1}>
                      <Box mr={2}>
                        {getDbIcon(
                          connection?.type || "unknown",
                          activeConnection?.id === connection?.id
                            ? "primary.main"
                            : "inherit"
                        )}
                      </Box>
                      <Typography
                        variant="h6"
                        component="div"
                        sx={{
                          fontWeight:
                            activeConnection?.id === connection?.id
                              ? "bold"
                              : "normal",
                        }}
                      >
                        {connection?.name || "Unnamed Connection"}
                      </Typography>
                    </Box>
                    <Box pl={6}>
                      <Typography variant="body2" color="textSecondary" noWrap>
                        {connection?.type || "Unknown"}
                        {connection?.host && ` • ${connection.host}`}
                        {connection?.port && `:${connection.port}`}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" noWrap>
                        {connection?.database ||
                          connection?.filename ||
                          "No database selected"}
                      </Typography>
                    </Box>
                  </CardActionArea>
                  <Box display="flex" p={1}>
                    <Button
                      size="small"
                      color="primary"
                      startIcon={<ArrowForwardIcon />}
                      onClick={() => connection && handleConnect(connection)}
                      sx={{ ml: 0.5, mb: 0.5 }}
                    >
                      Connect
                    </Button>
                    <Box flexGrow={1} />
                    <Tooltip title="Edit connection">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          connection && handleOpenDialog(connection);
                        }}
                        sx={{ mr: 0.5 }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete connection">
                      <IconButton
                        size="small"
                        onClick={(e) =>
                          connection?.id &&
                          handleDeleteConnection(connection.id, e)
                        }
                        color="error"
                        sx={{ mr: 0.5 }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Card>
              </Grid>
            ))}
        </Grid>
      )}

      {/* Connection Form Dialog */}
      <ConnectionForm
        open={dialogOpen}
        onClose={handleCloseDialog}
        connection={editingConnection}
        onSubmit={handleSubmitConnection}
        testConnection={testConnection}
        isEditing={!!editingConnection}
        loading={loading}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ConnectionsPage;
