import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Grid,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from "@mui/material";
import { useParams, useNavigate } from "react-router-dom";
import { useConnection } from "../contexts/ConnectionContext";
import SqlEditor from "../components/query/SqlEditor";
import QueryResults from "../components/query/QueryResults";
import QueryExecutionPanel from "../components/query/QueryExecutionPanel";
import SchemaBrowser from "../components/schema/SchemaBrowser";
import QueryHistory from "../components/query/QueryHistory";

const QueryPage = () => {
  const { connectionId } = useParams();
  const navigate = useNavigate();
  const { activeConnection, setActiveConnection, connections } =
    useConnection();

  // Query state
  const [query, setQuery] = useState("");
  const [queryResults, setQueryResults] = useState(null);
  const [queryLoading, setQueryLoading] = useState(false);
  const [queryError, setQueryError] = useState(null);
  const [executionTime, setExecutionTime] = useState(null);

  // Schema state
  const [schema, setSchema] = useState(null);
  const [selectedTable, setSelectedTable] = useState(null);

  // UI state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
  });
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [queryName, setQueryName] = useState("");
  const [queryDescription, setQueryDescription] = useState("");
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);

  // Set active connection from URL parameter
  useEffect(() => {
    if (connectionId && connections.length > 0) {
      const connection = connections.find((c) => c.id === connectionId);
      if (connection && connection.id !== activeConnection?.id) {
        setActiveConnection(connection);
      }
    }
  }, [connectionId, connections, activeConnection, setActiveConnection]);

  // Fetch schema when connection changes
  useEffect(() => {
    if (activeConnection) {
      fetchSchema();
    }
  }, [activeConnection]);

  const fetchSchema = async () => {
    if (!activeConnection) return;

    try {
      const response = await fetch(
        `/api/connections/${activeConnection.id}/schema`
      );
      if (response.ok) {
        const data = await response.json();
        setSchema(data);
      }
    } catch (error) {
      console.error("Failed to fetch schema:", error);
    }
  };

  const executeQuery = useCallback(async () => {
    if (!activeConnection || !query.trim()) {
      showSnackbar(
        "Please enter a query and ensure you have an active connection",
        "warning"
      );
      return;
    }

    setQueryLoading(true);
    setQueryError(null);
    setQueryResults(null);

    try {
      const startTime = Date.now();
      const response = await fetch("/api/query/execute", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          connectionId: activeConnection.id,
          query: query.trim(),
        }),
      });

      const data = await response.json();
      const endTime = Date.now();
      const execTime = `${endTime - startTime} ms`;

      if (response.ok) {
        setQueryResults(data);
        setExecutionTime(execTime);
        showSnackbar(`Query executed successfully in ${execTime}`, "success");
      } else {
        setQueryError(data.error || "Query execution failed");
        showSnackbar("Query execution failed", "error");
      }
    } catch (error) {
      setQueryError(error.message);
      showSnackbar("Network error occurred", "error");
    } finally {
      setQueryLoading(false);
    }
  }, [activeConnection, query]);

  const explainQuery = useCallback(async () => {
    if (!activeConnection || !query.trim()) {
      showSnackbar(
        "Please enter a query and ensure you have an active connection",
        "warning"
      );
      return;
    }

    setQueryLoading(true);
    setQueryError(null);

    try {
      const response = await fetch("/api/query/explain", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          connectionId: activeConnection.id,
          query: query.trim(),
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setQueryResults({
          rows: data.data.plan,
          rowCount: data.data.plan.length,
          fields: Object.keys(data.data.plan[0] || {}).map((key) => ({
            name: key,
          })),
        });
        showSnackbar("Query plan generated successfully", "success");
      } else {
        setQueryError(data.error || "Failed to generate query plan");
        showSnackbar("Failed to generate query plan", "error");
      }
    } catch (error) {
      setQueryError(error.message);
      showSnackbar("Network error occurred", "error");
    } finally {
      setQueryLoading(false);
    }
  }, [activeConnection, query]);

  const showSnackbar = (message, severity = "info") => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const handleTableSelect = (table) => {
    setSelectedTable(table.name);
    // Generate a simple SELECT query for the table
    const selectQuery = `SELECT * FROM ${table.name} LIMIT 100;`;
    setQuery(selectQuery);
  };

  const handleSaveQuery = () => {
    setSaveDialogOpen(true);
  };

  const handleSaveQueryConfirm = async () => {
    if (!queryName.trim()) {
      showSnackbar("Please enter a query name", "warning");
      return;
    }

    try {
      const response = await fetch("/api/query/save", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          connectionId: activeConnection.id,
          name: queryName,
          query: query,
          description: queryDescription,
        }),
      });

      if (response.ok) {
        showSnackbar("Query saved successfully", "success");
        setSaveDialogOpen(false);
        setQueryName("");
        setQueryDescription("");
      } else {
        showSnackbar("Failed to save query", "error");
      }
    } catch (error) {
      showSnackbar("Network error occurred", "error");
    }
  };

  const formatQuery = () => {
    // Simple SQL formatting - in a real app, you'd use a proper SQL formatter
    const formatted = query
      .replace(/\s+/g, " ")
      .replace(/,/g, ",\n  ")
      .replace(/FROM/gi, "\nFROM")
      .replace(/WHERE/gi, "\nWHERE")
      .replace(/JOIN/gi, "\nJOIN")
      .replace(/ORDER BY/gi, "\nORDER BY")
      .replace(/GROUP BY/gi, "\nGROUP BY")
      .replace(/HAVING/gi, "\nHAVING");

    setQuery(formatted);
    showSnackbar("Query formatted", "info");
  };

  if (!activeConnection) {
    return (
      <Box p={3}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          No active database connection. Please select a connection first.
        </Alert>
        <Button variant="contained" onClick={() => navigate("/connections")}>
          Go to Connections
        </Button>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "calc(100vh - 100px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Query Execution Panel */}
      <QueryExecutionPanel
        onExecute={executeQuery}
        onStop={() => setQueryLoading(false)}
        onSave={handleSaveQuery}
        onClear={() => setQuery("")}
        onFormat={formatQuery}
        onExplain={explainQuery}
        onShowHistory={() => setHistoryDialogOpen(true)}
        loading={queryLoading}
        canExecute={!!activeConnection}
        hasQuery={!!query.trim()}
        connection={activeConnection}
      />

      {/* Main Content */}
      <Box sx={{ flexGrow: 1, display: "flex", minHeight: 0 }}>
        <Grid container sx={{ height: "100%" }}>
          {/* Schema Browser */}
          <Grid
            item
            xs={12}
            md={3}
            sx={{ borderRight: 1, borderColor: "divider" }}
          >
            <SchemaBrowser
              onTableSelect={handleTableSelect}
              onRefresh={fetchSchema}
              selectedTable={selectedTable}
            />
          </Grid>

          {/* Query Editor and Results */}
          <Grid
            item
            xs={12}
            md={9}
            sx={{ display: "flex", flexDirection: "column" }}
          >
            {/* SQL Editor */}
            <Box sx={{ height: "40%", minHeight: "200px", p: 2 }}>
              <SqlEditor
                value={query}
                onChange={setQuery}
                onExecute={executeQuery}
                schema={schema}
                loading={queryLoading}
                height="100%"
              />
            </Box>

            {/* Query Results */}
            <Box sx={{ flexGrow: 1, p: 2, pt: 0 }}>
              <QueryResults
                data={queryResults}
                loading={queryLoading}
                error={queryError}
                executionTime={executionTime}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Save Query Dialog */}
      <Dialog
        open={saveDialogOpen}
        onClose={() => setSaveDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Save Query</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Query Name"
            fullWidth
            variant="outlined"
            value={queryName}
            onChange={(e) => setQueryName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (optional)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={queryDescription}
            onChange={(e) => setQueryDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveQueryConfirm} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Query History Dialog */}
      <QueryHistory
        open={historyDialogOpen}
        onClose={() => setHistoryDialogOpen(false)}
        onSelectQuery={(query) => {
          setQuery(query);
          setHistoryDialogOpen(false);
        }}
        connectionId={activeConnection?.id}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default QueryPage;
