{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/x-data-grid": "^7.22.2", "@mui/x-tree-view": "^7.22.1", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "8.12.0", "ajv-keywords": "5.1.0", "axios": "^1.10.0", "codemirror": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.34.2", "@codemirror/state": "^6.4.1", "@codemirror/commands": "^6.7.1", "@codemirror/search": "^6.5.8", "@codemirror/autocomplete": "^6.18.3", "@uiw/react-codemirror": "^4.23.5", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "lodash": "^4.17.21", "notistack": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "recharts": "^2.13.3", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}