{"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.1.2", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "Théo FIDRY <<EMAIL>>"], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": "visionmedia/bytes.js", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.2.0", "nyc": "15.1.0"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}