const express = require("express");
const router = express.Router();
const { connectionManager } = require("../db/connection");
const { BadRequestError } = require("../middleware/errorHandler");

// In-memory storage for connection configurations (in a real app, use a database)
const connectionConfigs = new Map();

// Get all database connections
router.get("/", async (req, res, next) => {
  try {
    // Return all stored connection configurations
    const connections = Array.from(connectionConfigs.values());

    res.json(connections);
  } catch (error) {
    next(error);
  }
});

// Test database connection
router.post("/test", async (req, res, next) => {
  try {
    const { type, ...config } = req.body;

    if (!type || !["postgresql", "mysql", "sqlite"].includes(type)) {
      throw new BadRequestError(
        "Invalid database type. Must be one of: postgresql, mysql, sqlite"
      );
    }

    // Basic validation based on database type
    if (type === "postgresql" || type === "mysql") {
      if (!config.host || !config.user || !config.database) {
        throw new BadRequestError(
          "Missing required connection parameters: host, user, database"
        );
      }
    } else if (type === "sqlite" && !config.filename) {
      throw new BadRequestError("SQLite requires a filename parameter");
    }

    // Create a temporary connection to test
    try {
      const connection = new (require(`../db/connection`).DatabaseConnection)(
        type,
        config
      );
      await connection.connect();
      await connection.close();

      res.json({
        success: true,
        message: "Connection successful",
        type,
      });
    } catch (error) {
      throw new BadRequestError(`Connection failed: ${error.message}`);
    }
  } catch (error) {
    next(error);
  }
});

// Create a new database connection
router.post("/", async (req, res, next) => {
  try {
    const { type, name, ...config } = req.body;

    if (!name || !type || !["postgresql", "mysql", "sqlite"].includes(type)) {
      throw new BadRequestError(
        "Missing required parameters: name and type (postgresql, mysql, or sqlite)"
      );
    }

    // Basic validation based on database type
    if (type === "postgresql" || type === "mysql") {
      if (!config.host || !config.user || !config.database) {
        throw new BadRequestError(
          "Missing required connection parameters: host, user, database"
        );
      }
    } else if (type === "sqlite" && !config.filename) {
      throw new BadRequestError("SQLite requires a filename parameter");
    }

    // Create connection ID based on name and timestamp
    const connectionId = `${name
      .toLowerCase()
      .replace(/\s+/g, "-")}-${Date.now()}`;

    // Store the connection configuration (in a real app, you'd store this in a database)
    const connectionConfig = {
      id: connectionId,
      name,
      type,
      ...config,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
    };

    // Store the connection configuration
    connectionConfigs.set(connectionId, connectionConfig);

    // Create and store the connection
    await connectionManager.createConnection({
      id: connectionId,
      type,
      ...config,
    });

    res.status(201).json({
      success: true,
      data: connectionConfig,
    });
  } catch (error) {
    next(error);
  }
});

// Get database schema
router.get("/:connectionId/schema", async (req, res, next) => {
  try {
    const { connectionId } = req.params;

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError("Invalid connection ID");
    }

    const schema = await connection.getSchema();

    res.json({
      success: true,
      ...schema,
    });
  } catch (error) {
    next(error);
  }
});

// Update a database connection
router.put("/:connectionId", async (req, res, next) => {
  try {
    const { connectionId } = req.params;
    const { type, name, ...config } = req.body;

    if (!connectionConfigs.has(connectionId)) {
      throw new BadRequestError("Connection not found");
    }

    if (!name || !type || !["postgresql", "mysql", "sqlite"].includes(type)) {
      throw new BadRequestError(
        "Missing required parameters: name and type (postgresql, mysql, or sqlite)"
      );
    }

    // Basic validation based on database type
    if (type === "postgresql" || type === "mysql") {
      if (!config.host || !config.user || !config.database) {
        throw new BadRequestError(
          "Missing required connection parameters: host, user, database"
        );
      }
    } else if (type === "sqlite" && !config.filename) {
      throw new BadRequestError("SQLite requires a filename parameter");
    }

    // Update the connection configuration
    const existingConfig = connectionConfigs.get(connectionId);
    const updatedConfig = {
      ...existingConfig,
      name,
      type,
      ...config,
      lastUsed: new Date().toISOString(),
    };

    // Store the updated configuration
    connectionConfigs.set(connectionId, updatedConfig);

    // Update the connection in the manager
    await connectionManager.closeConnection(connectionId);
    await connectionManager.createConnection({
      id: connectionId,
      type,
      ...config,
    });

    res.json({
      success: true,
      data: updatedConfig,
    });
  } catch (error) {
    next(error);
  }
});

// Delete a database connection
router.delete("/:connectionId", async (req, res, next) => {
  try {
    const { connectionId } = req.params;

    if (!connectionConfigs.has(connectionId)) {
      throw new BadRequestError("Connection not found");
    }

    // Remove from storage
    connectionConfigs.delete(connectionId);

    // Close the connection
    await connectionManager.closeConnection(connectionId);

    res.json({
      success: true,
      message: "Connection deleted successfully",
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
