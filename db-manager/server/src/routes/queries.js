const express = require("express");
const router = express.Router();
const { connectionManager } = require("../db/connection");
const { BadRequestError } = require("../middleware/errorHandler");

// In-memory storage for query history (in production, use a database)
const queryHistory = new Map(); // connectionId -> array of queries

// Execute a query
router.post("/execute", async (req, res, next) => {
  try {
    const { connectionId, query, params = [] } = req.body;

    if (!connectionId || !query) {
      throw new BadRequestError(
        "Missing required parameters: connectionId and query"
      );
    }

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError("Invalid connection ID");
    }

    // Execute the query
    const startTime = process.hrtime();
    const result = await connection.query(query, params);
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const executionTime = (seconds * 1000 + nanoseconds / 1e6).toFixed(2); // in milliseconds

    // Format the response
    const response = {
      success: true,
      executionTime: `${executionTime} ms`,
      rowCount: result.rowCount || 0,
      fields: result.fields || [],
      rows: result.rows || [],
      command: query.trim().split(" ")[0].toUpperCase(),
      // Add pagination info if needed
      pagination: {
        total: result.rowCount || 0,
        page: 1,
        pageSize: result.rows ? result.rows.length : 0,
      },
    };

    // Add to query history
    if (!queryHistory.has(connectionId)) {
      queryHistory.set(connectionId, []);
    }

    const history = queryHistory.get(connectionId);
    history.unshift({
      id: Date.now(),
      query: query,
      executedAt: new Date(),
      executionTime: `${executionTime}ms`,
      rowCount: result.rowCount || result.rows?.length || 0,
      status: "success",
    });

    // Keep only last 100 queries
    if (history.length > 100) {
      history.splice(100);
    }

    res.json(response);
  } catch (error) {
    // Add failed query to history
    if (!queryHistory.has(connectionId)) {
      queryHistory.set(connectionId, []);
    }

    const history = queryHistory.get(connectionId);
    history.unshift({
      id: Date.now(),
      query: query,
      executedAt: new Date(),
      executionTime: null,
      rowCount: null,
      status: "error",
      error: error.message,
    });

    if (history.length > 100) {
      history.splice(100);
    }

    // Format database errors to be more user-friendly
    if (error.message.includes("syntax error")) {
      error = new BadRequestError("SQL Syntax Error: " + error.message);
    } else if (error.message.includes("does not exist")) {
      error = new BadRequestError(
        "Database object not found: " + error.message
      );
    } else if (error.message.includes("duplicate key")) {
      error = new BadRequestError("Duplicate key violation: " + error.message);
    } else if (error.message.includes("violates foreign key constraint")) {
      error = new BadRequestError("Foreign key violation: " + error.message);
    } else if (error.message.includes("permission denied")) {
      error = new Error("Permission denied: " + error.message);
      error.statusCode = 403;
    }

    next(error);
  }
});

// Get query history for a connection
router.get("/history/:connectionId", async (req, res, next) => {
  try {
    const { connectionId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    const history = queryHistory.get(connectionId) || [];
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedHistory = history.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedHistory,
      pagination: {
        total: history.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
      },
    });
  } catch (error) {
    next(error);
  }
});

// Save a query (stub - in a real app, this would save to a database)
router.post("/save", async (req, res, next) => {
  try {
    const { connectionId, name, query, description = "" } = req.body;

    if (!connectionId || !name || !query) {
      throw new BadRequestError(
        "Missing required parameters: connectionId, name, and query"
      );
    }

    // In a real app, you would save this to a database
    const savedQuery = {
      id: `query-${Date.now()}`,
      connectionId,
      name,
      query,
      description,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    res.status(201).json({
      success: true,
      data: savedQuery,
    });
  } catch (error) {
    next(error);
  }
});

// Get saved queries for a connection (stub - in a real app, this would query a database)
router.get("/saved/:connectionId", async (req, res, next) => {
  try {
    const { connectionId } = req.params;

    // In a real app, you would query a database for saved queries
    // This is just a stub that returns an empty array
    const savedQueries = [];

    res.json({
      success: true,
      data: savedQueries,
    });
  } catch (error) {
    next(error);
  }
});

// Get query execution plan
router.post("/explain", async (req, res, next) => {
  try {
    const { connectionId, query, params = [] } = req.body;

    if (!connectionId || !query) {
      throw new BadRequestError(
        "Missing required parameters: connectionId and query"
      );
    }

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError("Invalid connection ID");
    }

    let explainQuery;
    switch (connection.type) {
      case "postgresql":
        explainQuery = `EXPLAIN ANALYZE ${query}`;
        break;
      case "mysql":
        explainQuery = `EXPLAIN FORMAT=JSON ${query}`;
        break;
      case "sqlite":
        explainQuery = `EXPLAIN QUERY PLAN ${query}`;
        break;
      default:
        throw new BadRequestError("Unsupported database type for EXPLAIN");
    }

    const result = await connection.query(explainQuery, params);

    res.json({
      success: true,
      data: {
        plan: result.rows,
        query: explainQuery,
        databaseType: connection.type,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
