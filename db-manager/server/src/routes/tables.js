const express = require('express');
const router = express.Router();
const { connectionManager } = require('../db/connection');
const { BadRequestError } = require('../middleware/errorHandler');

// Create a new table
router.post('/:connectionId/tables', async (req, res) => {
  try {
    const { connectionId } = req.params;
    const { name, columns, sql } = req.body;

    if (!name || !columns || columns.length === 0) {
      throw new BadRequestError('Table name and columns are required');
    }

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError('Connection not found');
    }

    // Execute the CREATE TABLE SQL
    await connection.executeQuery(sql || generateCreateTableSQL(name, columns, connection.type));

    res.json({
      success: true,
      message: `Table "${name}" created successfully`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update an existing table (ALTER TABLE)
router.put('/:connectionId/tables/:tableName', async (req, res) => {
  try {
    const { connectionId, tableName } = req.params;
    const { columns, sql } = req.body;

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError('Connection not found');
    }

    // For now, we'll use the provided SQL or generate basic ALTER statements
    if (sql) {
      await connection.executeQuery(sql);
    } else {
      // Generate ALTER TABLE statements based on column changes
      // This is a simplified implementation - in production, you'd want more sophisticated diffing
      throw new BadRequestError('SQL statement required for table modifications');
    }

    res.json({
      success: true,
      message: `Table "${tableName}" updated successfully`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Delete a table
router.delete('/:connectionId/tables/:tableName', async (req, res) => {
  try {
    const { connectionId, tableName } = req.params;

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError('Connection not found');
    }

    // Execute DROP TABLE
    await connection.executeQuery(`DROP TABLE ${tableName}`);

    res.json({
      success: true,
      message: `Table "${tableName}" deleted successfully`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get table data with pagination
router.get('/:connectionId/tables/:tableName/data', async (req, res) => {
  try {
    const { connectionId, tableName } = req.params;
    const { page = 1, limit = 100, orderBy, orderDirection = 'ASC' } = req.query;

    const connection = connectionManager.getConnection(connectionId);
    if (!connection) {
      throw new BadRequestError('Connection not found');
    }

    const offset = (page - 1) * limit;
    let query = `SELECT * FROM ${tableName}`;
    
    if (orderBy) {
      query += ` ORDER BY ${orderBy} ${orderDirection}`;
    }
    
    query += ` LIMIT ${limit} OFFSET ${offset}`;

    const result = await connection.executeQuery(query);

    // Get total count
    const countResult = await connection.executeQuery(`SELECT COUNT(*) as total FROM ${tableName}`);
    const total = countResult.rows[0].total;

    res.json({
      success: true,
      data: {
        rows: result.rows,
        fields: result.fields,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(total),
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Helper function to generate CREATE TABLE SQL
function generateCreateTableSQL(tableName, columns, dbType) {
  let sql = `CREATE TABLE ${tableName} (\n`;
  
  const columnDefinitions = columns.map(col => {
    let def = `  ${col.name} ${col.type}`;
    
    // Add length for VARCHAR types
    if (col.type === 'VARCHAR' && col.length) {
      def += `(${col.length})`;
    }
    
    // Add constraints
    if (col.isPrimaryKey) {
      def += ' PRIMARY KEY';
    }
    if (col.isNotNull && !col.isPrimaryKey) {
      def += ' NOT NULL';
    }
    if (col.defaultValue) {
      def += ` DEFAULT ${col.defaultValue}`;
    }
    
    return def;
  });

  sql += columnDefinitions.join(',\n');
  sql += '\n);';
  
  return sql;
}

module.exports = router;
