const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const rateLimit = require("express-rate-limit");
const { connectToDatabase } = require("./db/connection");
const { errorHandler } = require("./middleware/errorHandler");

// Import routes
const connectionRoutes = require("./routes/connections");
const queryRoutes = require("./routes/queries");
const tableRoutes = require("./routes/tables");

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Rate limiting
const limiter = rateLimit({
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Routes
app.use("/api/connections", connectionRoutes);
app.use("/api/query", queryRoutes);
app.use("/api", tableRoutes);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok", timestamp: new Date().toISOString() });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({ error: "Not Found" });
});

// Error handling middleware
app.use(errorHandler);

module.exports = app;
