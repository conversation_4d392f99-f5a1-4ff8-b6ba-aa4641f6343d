{"name": "db-manager-server", "version": "1.0.0", "description": "Database Management UI - Backend Server", "main": "src/index.js", "type": "commonjs", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/", "format": "prettier --write \"src/**/*.js\""}, "keywords": ["database", "management", "postgresql", "mysql", "sqlite", "admin", "ui"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^7.0.0", "joi": "^17.13.3", "knex": "^3.1.0", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "pg": "^8.16.2", "sqlite3": "^5.1.7", "uuid": "^11.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}